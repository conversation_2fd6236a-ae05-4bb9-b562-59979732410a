# 记账管理系统 - 项目重构技术规范文档

## 文档信息
- **文档版本**: 2.0
- **创建日期**: 2025-08-01
- **最后更新**: 2025-08-02
- **文档目的**: 为项目重构提供完整的技术规范和实现指南（已更新为现代化技术栈）

## 目录
1. [项目概述](#1-项目概述)
2. [技术架构](#2-技术架构)
3. [功能模块详解](#3-功能模块详解)
4. [计算逻辑规范](#4-计算逻辑规范)
5. [数据结构定义](#5-数据结构定义)
6. [API接口规范](#6-api接口规范)
7. [业务规则说明](#7-业务规则说明)
8. [配置参数说明](#8-配置参数说明)
9. [依赖关系图](#9-依赖关系图)
10. [部署和运维](#10-部署和运维)

---

## 1. 项目概述

### 1.1 项目目标
现代化记账管理系统，支持多账本管理、智能记账、数据统计分析和导出功能。系统采用前后端分离架构，提供Web界面和API接口。

### 1.2 主要功能
- **用户管理**: 用户注册、登录、认证
- **账本管理**: 多账本创建、编辑、删除
- **记账记录**: 记录创建、编辑、状态管理
- **智能计算**: 累计金额、递减形式、续期计算
- **统计分析**: 数据统计、趋势分析、图表展示
- **数据导出**: CSV、JSON格式导出
- **回收站**: 软删除和恢复机制
- **月度管理**: 月度状态跟踪和锁定

### 1.3 核心特性
- **递减记账**: 支持金额递减形式的记账方式
- **月度状态**: 每个记录在不同月份的独立状态管理
- **续期计算**: 智能续期周期计算（1/2/3/6个月）
- **实时统计**: 基于当前数据的实时计算
- **多级缓存**: 内存缓存 + LocalStorage缓存
- **性能监控**: 请求性能和资源使用监控

---

## 2. 技术架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端API       │    │   数据库        │
│                 │    │                 │    │                 │
│ React 19        │◄──►│ Node.js API     │◄──►│ PostgreSQL     │
│ TypeScript      │    │ Express.js      │    │ JSONB          │
│ Tailwind CSS    │    │ RESTful         │    │ UTF-8          │
│ ShadcnUI        │    │ JWT Auth        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   缓存层        │    │   监控层        │    │   日志系统      │
│                 │    │                 │    │                 │
│ Memory Cache    │    │ Performance     │    │ File Logging    │
│ LocalStorage    │    │ Error Monitor   │    │ Error Tracking  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 技术栈详情

#### 前端技术栈
- **运行时环境**: Node.js (最新 LTS 版本)
- **前端框架**: React 19 (最新版本)
- **类型系统**: TypeScript (严格模式)
- **样式框架**: Tailwind CSS V4 (最新版本)
- **UI组件库**: ShadcnUI (基于 Radix UI 的现代组件库)
- **图标库**: Font Awesome (完整图标集)
- **构建工具**: Vite (最新版本)
- **状态管理**: Zustand 或 React Context API
- **路由管理**: React Router v6
- **HTTP客户端**: Axios 或 Fetch API
- **日期处理**: Day.js
- **工具库**: Lodash-es

#### 后端技术栈
- **运行时环境**: Node.js (最新 LTS 版本)
- **Web框架**: Express.js (最新版本)
- **开发语言**: TypeScript (严格模式)
- **数据库**: PostgreSQL (最新稳定版本)
- **ORM/查询构建器**: Prisma 或 TypeORM
- **认证方式**: JWT (JSON Web Token)
- **API风格**: RESTful API
- **数据验证**: Zod 或 Joi
- **日志记录**: Winston 或 Pino
- **进程管理**: PM2

#### 开发工具
- **代码检查**: ESLint + TypeScript ESLint
- **代码格式化**: Prettier
- **测试框架**: Jest + Supertest (后端) / Vitest + React Testing Library (前端)
- **API文档**: Swagger/OpenAPI
- **类型检查**: TypeScript 严格模式
- **数据库迁移**: Prisma Migrate 或 TypeORM Migrations

---

## 3. 功能模块详解

### 3.1 用户认证模块

#### 3.1.1 功能描述
负责用户注册、登录、JWT token管理和权限验证。

#### 3.1.2 核心流程
1. **用户注册**:
   - 输入: username, email, password
   - 验证: 用户名唯一性、邮箱格式、密码强度
   - 处理: 密码哈希、创建用户、生成默认账本
   - 输出: JWT token + 用户信息

2. **用户登录**:
   - 输入: username/email, password
   - 验证: 用户存在性、密码正确性
   - 处理: 生成JWT token
   - 输出: JWT token + 用户信息

3. **Token验证**:
   - 输入: Authorization Header
   - 验证: Token有效性、过期时间
   - 处理: 解析用户信息
   - 输出: 用户身份信息

#### 3.1.3 安全机制
- 密码使用 `password_hash()` 进行bcrypt哈希
- JWT token 7天有效期
- 支持token刷新机制
- SQL预处理防注入
- XSS过滤和CORS控制

### 3.2 账本管理模块

#### 3.2.1 功能描述
管理用户的多个账本，支持创建、编辑、删除和权限控制。

#### 3.2.2 核心流程
1. **创建账本**:
   - 输入: name, description
   - 验证: 名称非空、用户权限
   - 处理: 插入数据库、关联用户
   - 输出: 账本信息

2. **编辑账本**:
   - 输入: book_id, name, description
   - 验证: 账本存在性、用户权限
   - 处理: 更新数据库记录
   - 输出: 更新后账本信息

3. **删除账本**:
   - 输入: book_id
   - 验证: 账本存在性、用户权限、非最后账本
   - 处理: 记录移至回收站、删除账本
   - 输出: 删除结果和移动记录数

#### 3.2.3 特殊规则
- 用户至少保留一个账本
- 删除账本时记录自动移至回收站
- 支持回收站账本自动创建
- 账本名称在用户范围内唯一

### 3.3 记账记录模块

#### 3.3.1 功能描述
管理记账记录的创建、编辑、状态管理和计算逻辑。

#### 3.3.2 数据字段说明
- `amount`: 原始金额（总金额）
- `monthly_amount`: 每月金额
- `renewal_amount`: 续期金额
- `renewal_time`: 续期周期（一个月/二个月/三个月/六个月/永久）
- `accumulated_amount`: 累计金额
- `is_decreasing`: 是否为递减形式
- `remaining_amount`: 剩余金额（递减形式使用）
- `is_finished`: 是否已结束（递减形式清零后）

#### 3.3.3 核心流程
1. **创建记录**:
   - 输入: 所有必填字段
   - 验证: 字段完整性、数据格式、账本权限
   - 处理: 插入记录、初始化状态
   - 输出: 记录信息

2. **编辑记录**:
   - 输入: record_id + 更新字段
   - 验证: 记录存在性、用户权限
   - 处理: 更新记录、重新计算相关金额
   - 输出: 更新后记录信息

3. **状态管理**:
   - 月度状态独立管理
   - 支持历史月份状态查看
   - 实时计算累计金额和剩余金额

---

## 4. 计算逻辑规范

### 4.1 基础金额计算

#### 4.1.1 金额字段定义
```typescript
// 基础金额字段类型定义
interface RecordAmountFields {
    amount: number;           // 原始总金额
    monthly_amount: number;   // 每月金额
    renewal_amount: number;   // 续期金额
    renewal_time: string;     // 续期周期
    accumulated_amount: number; // 累计金额
    remaining_amount: number; // 剩余金额（递减形式）
}

// 示例记录数据
const record: RecordAmountFields = {
    amount: 1000.00,
    monthly_amount: 100.00,
    renewal_amount: 120.00,
    renewal_time: "三个月",
    accumulated_amount: 300.00,
    remaining_amount: 700.00
};
```

#### 4.1.2 续期月份判断算法
```typescript
// 续期周期映射类型
type RenewalTimeMap = {
    [key: string]: number;
};

// 续期月份判断函数
function isRenewalMonth(record: Record, viewMonth: string): boolean {
    const monthsToAdd: RenewalTimeMap = {
        '一个月': 1,
        '二个月': 2,
        '三个月': 3,
        '六个月': 6
    };

    const addMonths = monthsToAdd[record.renewal_time];
    if (!addMonths || record.renewal_time === '永久') {
        return false;
    }

    const recordDate = new Date(record.date);
    const viewDate = new Date(viewMonth + '-01');

    // 计算月份差
    const monthDiff = (viewDate.getFullYear() - recordDate.getFullYear()) * 12
                    + (viewDate.getMonth() - recordDate.getMonth());

    // 判断是否为续期月份
    return monthDiff > 0 && monthDiff % addMonths === 0;
}

// React Hook 形式的续期判断
export const useRenewalCalculation = () => {
    const checkIsRenewalMonth = useCallback((record: Record, viewMonth: string): boolean => {
        return isRenewalMonth(record, viewMonth);
    }, []);

    return { checkIsRenewalMonth };
};
```

### 4.2 累计金额计算

#### 4.2.1 计算公式
```
累计金额 = Σ(已完成月份的金额)

其中每月金额 = {
    续期金额,     如果当月为续期月份
    每月金额,     如果当月为普通月份
}
```

#### 4.2.2 实现算法
```typescript
// 累计金额计算函数
function calculateAccumulatedAmount(recordId: number, currentMonth: string): number {
    // 查询所有已完成的月份状态
    const completedMonths = getCompletedMonths(recordId, currentMonth);

    let totalAmount = 0;
    for (const month of completedMonths) {
        const isRenewal = isRenewalMonth(record, month);
        const amount = isRenewal ?
            parseFloat(record.renewal_amount.toString()) :
            parseFloat(record.monthly_amount.toString());
        totalAmount += amount;
    }

    return totalAmount;
}

// React Hook 形式的累计金额计算
export const useAccumulatedAmountCalculation = () => {
    const calculateAmount = useCallback((recordId: number, currentMonth: string): number => {
        return calculateAccumulatedAmount(recordId, currentMonth);
    }, []);

    // 使用 useMemo 缓存计算结果
    const memoizedCalculateAmount = useMemo(() => calculateAmount, [calculateAmount]);

    return { calculateAccumulatedAmount: memoizedCalculateAmount };
};
```

### 4.3 递减形式计算

#### 4.3.1 递减逻辑说明
递减形式记账是指随着时间推移，记录的剩余金额逐渐减少，直至清零的记账方式。

#### 4.3.2 计算公式
```
剩余金额 = 原始金额 - 累计金额
历史剩余金额 = 原始金额 - 截止到指定月份的累计金额

当剩余金额 <= 0 时，标记为已结束 (is_finished = 1)
```

#### 4.3.3 实现算法
```typescript
// 历史剩余金额计算函数
function calculateHistoricalRemainingAmount(record: Record, viewMonth: string): number {
    const originalAmount = parseFloat(record.amount?.toString() || '0');
    const accumulatedAmount = parseFloat(record.accumulated_amount?.toString() || '0');

    // 历史剩余金额 = 原始金额 - 截止到查看月份的累计金额
    const historicalRemainingAmount = originalAmount - accumulatedAmount;

    // 确保不为负数
    return Math.max(0, historicalRemainingAmount);
}

// 递减记录更新函数
function updateDecreasingRecord(record: Record, isCompleted: boolean, currentMonth: string): Record {
    const updatedRecord = { ...record };

    if (updatedRecord.is_decreasing) {
        const isRenewal = isRenewalMonth(updatedRecord, currentMonth);
        const amount = isRenewal ?
            parseFloat(updatedRecord.renewal_amount.toString()) :
            parseFloat(updatedRecord.monthly_amount.toString());

        if (isCompleted) {
            // 完成时减少剩余金额
            updatedRecord.remaining_amount -= amount;
            updatedRecord.accumulated_amount += amount;

            // 检查是否结束
            if (updatedRecord.remaining_amount <= 0) {
                updatedRecord.remaining_amount = 0;
                updatedRecord.is_finished = true;
            }
        } else {
            // 取消完成时恢复剩余金额
            updatedRecord.remaining_amount += amount;
            updatedRecord.accumulated_amount = Math.max(0, updatedRecord.accumulated_amount - amount);

            // 恢复结束状态
            if (updatedRecord.remaining_amount > 0) {
                updatedRecord.is_finished = false;
            }
        }
    }

    return updatedRecord;
}

// React Hook 形式的递减计算
export const useDecreasingCalculation = () => {
    const calculateHistoricalRemaining = useCallback((record: Record, viewMonth: string): number => {
        return calculateHistoricalRemainingAmount(record, viewMonth);
    }, []);

    const updateDecreasing = useCallback((record: Record, isCompleted: boolean, currentMonth: string): Record => {
        return updateDecreasingRecord(record, isCompleted, currentMonth);
    }, []);

    return {
        calculateHistoricalRemainingAmount: calculateHistoricalRemaining,
        updateDecreasingRecord: updateDecreasing
    };
};
```

### 4.4 统计计算

#### 4.4.1 总览统计
```typescript
// 统计结果类型定义
interface CalculatedOverviewStats {
    totalAmount: number;
    totalMonthlyAmount: number;
    totalRenewalAmount: number;
    totalAccumulatedAmount: number;
    currentMonthAccumulated: number;
}

// 总览统计计算函数
function calculateOverviewStats(records: Record[], currentViewMonth: string): CalculatedOverviewStats {
    return records.reduce((acc, record) => {
        // 根据记录类型计算显示金额
        let displayAmount: number;
        if (record.is_decreasing) {
            displayAmount = calculateHistoricalRemainingAmount(record, currentViewMonth);
        } else {
            displayAmount = parseFloat(record.amount?.toString() || '0');
        }

        acc.totalAmount += displayAmount;
        acc.totalMonthlyAmount += parseFloat(record.monthly_amount?.toString() || '0');
        acc.totalRenewalAmount += parseFloat(record.renewal_amount?.toString() || '0');
        acc.totalAccumulatedAmount += parseFloat(record.accumulated_amount?.toString() || '0');

        // 计算当月累积金额
        if (record.current_completed) {
            const isRenewal = isRenewalMonth(record, currentViewMonth);
            const monthlyAmount = isRenewal ?
                parseFloat(record.renewal_amount?.toString() || '0') :
                parseFloat(record.monthly_amount?.toString() || '0');
            acc.currentMonthAccumulated += monthlyAmount;
        }

        return acc;
    }, {
        totalAmount: 0,
        totalMonthlyAmount: 0,
        totalRenewalAmount: 0,
        totalAccumulatedAmount: 0,
        currentMonthAccumulated: 0
    } as CalculatedOverviewStats);
}

// React Hook 形式的统计计算
export const useOverviewStatsCalculation = () => {
    const calculateStats = useCallback((records: Record[], currentViewMonth: string): CalculatedOverviewStats => {
        return calculateOverviewStats(records, currentViewMonth);
    }, []);

    // 使用 useMemo 缓存计算结果
    const memoizedCalculateStats = useMemo(() => calculateStats, [calculateStats]);

    return { calculateOverviewStats: memoizedCalculateStats };
};
```

---

## 5. 数据结构定义

### 5.1 数据库表结构

#### 5.1.1 用户表 (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE
    ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### 5.1.2 账本表 (account_books)
```sql
CREATE TABLE account_books (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_recycle_bin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建更新时间触发器
CREATE TRIGGER update_account_books_updated_at BEFORE UPDATE
    ON account_books FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建用户账本名称唯一约束
CREATE UNIQUE INDEX idx_user_book_name ON account_books(user_id, name);
```

#### 5.1.3 记录表 (records)
```sql
CREATE TABLE records (
    id SERIAL PRIMARY KEY,
    account_book_id INTEGER NOT NULL,
    date DATE NOT NULL,
    name VARCHAR(100) NOT NULL,
    amount NUMERIC(10,2) NOT NULL,
    monthly_amount NUMERIC(10,2) NOT NULL,
    renewal_time VARCHAR(50) NOT NULL,
    renewal_amount NUMERIC(10,2) NOT NULL,
    remark TEXT,
    accumulated_amount NUMERIC(10,2) DEFAULT 0,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_month VARCHAR(7),
    is_locked BOOLEAN DEFAULT FALSE,
    is_decreasing BOOLEAN DEFAULT FALSE,
    remaining_amount NUMERIC(10,2) DEFAULT 0,
    is_finished BOOLEAN DEFAULT FALSE,
    original_book_id INTEGER NULL,
    deleted_at TIMESTAMPTZ NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_book_id) REFERENCES account_books(id) ON DELETE CASCADE,
    FOREIGN KEY (original_book_id) REFERENCES account_books(id) ON DELETE SET NULL
);

-- 创建更新时间触发器
CREATE TRIGGER update_records_updated_at BEFORE UPDATE
    ON records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建续期时间检查约束
ALTER TABLE records ADD CONSTRAINT check_renewal_time
    CHECK (renewal_time IN ('一个月', '二个月', '三个月', '六个月', '永久'));
```

#### 5.1.4 月份状态表 (record_monthly_states)
```sql
CREATE TABLE record_monthly_states (
    id SERIAL PRIMARY KEY,
    record_id INTEGER NOT NULL,
    view_month VARCHAR(7) NOT NULL, -- 格式: 2024-05
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMPTZ NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (record_id) REFERENCES records(id) ON DELETE CASCADE,
    UNIQUE (record_id, view_month)
);

-- 创建更新时间触发器
CREATE TRIGGER update_record_monthly_states_updated_at BEFORE UPDATE
    ON record_monthly_states FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建索引
CREATE INDEX idx_view_month ON record_monthly_states(view_month);
CREATE INDEX idx_is_completed ON record_monthly_states(is_completed);
CREATE INDEX idx_record_month_completed ON record_monthly_states(record_id, view_month, is_completed);

-- 创建月份格式检查约束
ALTER TABLE record_monthly_states ADD CONSTRAINT check_view_month_format
    CHECK (view_month ~ '^\d{4}-\d{2}$');
```

### 5.2 数据库索引优化
```sql
-- 优化账本记录查询
CREATE INDEX idx_records_book_completed ON records(account_book_id, is_completed);
CREATE INDEX idx_records_book_date ON records(account_book_id, date);
CREATE INDEX idx_records_book_decreasing ON records(account_book_id, is_decreasing);

-- 优化统计查询
CREATE INDEX idx_records_completed_month ON records(is_completed, completed_month);
CREATE INDEX idx_records_renewal_time ON records(renewal_time);

-- 优化软删除查询
CREATE INDEX idx_records_deleted_at ON records(deleted_at) WHERE deleted_at IS NOT NULL;
CREATE INDEX idx_records_active ON records(account_book_id) WHERE deleted_at IS NULL;

-- 优化递减记录查询
CREATE INDEX idx_records_decreasing_finished ON records(is_decreasing, is_finished) WHERE is_decreasing = TRUE;

-- 优化月份状态查询（已在表创建时定义）
-- CREATE INDEX idx_record_month_completed ON record_monthly_states(record_id, view_month, is_completed);

-- 复合索引优化
CREATE INDEX idx_records_book_date_decreasing ON records(account_book_id, date, is_decreasing);
CREATE INDEX idx_monthly_states_month_completed ON record_monthly_states(view_month, is_completed, record_id);

-- 部分索引优化（PostgreSQL 特有）
CREATE INDEX idx_records_active_completed ON records(account_book_id, is_completed)
    WHERE deleted_at IS NULL;
CREATE INDEX idx_records_active_decreasing ON records(account_book_id, is_decreasing)
    WHERE deleted_at IS NULL AND is_decreasing = TRUE;
```

### 5.3 前端数据模型

#### 5.3.1 用户数据模型
```typescript
// 用户基础信息接口
interface User {
    id: number;
    username: string;
    email: string;
    created_at: string;
    updated_at: string;
}

// 认证响应接口
interface AuthResponse {
    token: string;
    user: User;
}

// 用户状态管理类型
type UserState = {
    user: User | null;
    token: string | null;
    isAuthenticated: boolean;
};
```

#### 5.3.2 账本数据模型
```typescript
// 账本基础信息接口
interface AccountBook {
    id: number;
    user_id: number;
    name: string;
    description: string;
    is_recycle_bin: boolean;
    created_at: string;
    updated_at: string;
}

// 账本创建/编辑表单类型
type AccountBookForm = Omit<AccountBook, 'id' | 'user_id' | 'is_recycle_bin' | 'created_at' | 'updated_at'>;

// 账本状态管理类型
type AccountBookState = {
    books: AccountBook[];
    currentBook: AccountBook | null;
    loading: boolean;
};
```

#### 5.3.3 记录数据模型
```typescript
// 记录基础信息接口
interface Record {
    id: number;
    account_book_id: number;
    date: string;
    name: string;
    amount: number;
    monthly_amount: number;
    renewal_time: string;
    renewal_amount: number;
    remark: string;
    accumulated_amount: number;
    is_completed: boolean;
    completed_month: string;
    is_locked: boolean;
    is_decreasing: boolean;
    remaining_amount: number;
    is_finished: boolean;
    current_completed?: boolean; // 当前月份完成状态
    created_at: string;
    updated_at: string;
}

// 记录创建/编辑表单类型
type RecordForm = Omit<Record, 'id' | 'accumulated_amount' | 'is_completed' | 'completed_month' | 'is_locked' | 'remaining_amount' | 'is_finished' | 'current_completed' | 'created_at' | 'updated_at'>;

// 记录状态管理类型
type RecordState = {
    records: Record[];
    currentMonth: string;
    loading: boolean;
    filters: {
        search: string;
        status: 'all' | 'completed' | 'pending';
        type: 'all' | 'decreasing' | 'normal';
    };
};
```

#### 5.3.4 统计数据模型
```typescript
// 总览统计接口
interface OverviewStats {
    total_books: number;
    total_records: number;
    completed_records: number;
    total_accumulated: number;
    monthly_income: number;
    month_records: number;
    month_completed: number;
    month_income: number;
    month_accumulated: number;
}

// 月度统计接口
interface MonthlyStats {
    month: string;
    total_amount: number;
    completed_amount: number;
    completion_rate: number;
}

// 趋势统计接口
interface TrendStats {
    date: string;
    amount: number;
    type: 'income' | 'expense';
}

// 统计状态管理类型
type StatsState = {
    overview: OverviewStats | null;
    monthly: MonthlyStats[];
    trends: TrendStats[];
    loading: boolean;
};
```

---

## 6. API接口规范

### 6.1 认证接口

#### 6.1.1 用户注册
```
POST /api/auth/register
Content-Type: application/json

Request Body:
{
    "username": "string",
    "email": "string", 
    "password": "string"
}

Response:
{
    "success": true,
    "message": "注册成功",
    "data": {
        "token": "jwt_token_string",
        "user": {
            "id": 1,
            "username": "testuser",
            "email": "<EMAIL>"
        }
    }
}
```

#### 6.1.2 用户登录
```
POST /api/auth/login
Content-Type: application/json

Request Body:
{
    "username": "string", // 用户名或邮箱
    "password": "string"
}

Response:
{
    "success": true,
    "message": "登录成功",
    "data": {
        "token": "jwt_token_string",
        "user": {
            "id": 1,
            "username": "testuser",
            "email": "<EMAIL>"
        }
    }
}
```

### 6.2 账本管理接口

#### 6.2.1 获取账本列表
```
GET /api/account-books
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "默认账本",
            "description": "系统自动创建的默认账本",
            "is_recycle_bin": false,
            "created_at": "2024-01-01 00:00:00",
            "updated_at": "2024-01-01 00:00:00"
        }
    ]
}
```

#### 6.2.2 创建账本
```
POST /api/account-books
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
    "name": "string",
    "description": "string"
}

Response:
{
    "success": true,
    "message": "账本创建成功",
    "data": {
        "id": 2,
        "name": "新账本",
        "description": "账本描述",
        "is_recycle_bin": false,
        "created_at": "2024-01-01 00:00:00",
        "updated_at": "2024-01-01 00:00:00"
    }
}
```

### 6.3 记录管理接口

#### 6.3.1 获取记录列表
```
GET /api/records/{book_id}?month={YYYY-MM}
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": [
        {
            "id": 1,
            "account_book_id": 1,
            "date": "2024-01-01",
            "name": "记录名称",
            "amount": 1000.00,
            "monthly_amount": 100.00,
            "renewal_time": "三个月",
            "renewal_amount": 120.00,
            "remark": "备注信息",
            "accumulated_amount": 300.00,
            "is_completed": false,
            "completed_month": null,
            "is_locked": false,
            "is_decreasing": false,
            "remaining_amount": 700.00,
            "is_finished": false,
            "current_completed": false,
            "created_at": "2024-01-01 00:00:00",
            "updated_at": "2024-01-01 00:00:00"
        }
    ]
}
```

#### 6.3.2 创建记录
```
POST /api/records/{book_id}
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
    "date": "2024-01-01",
    "name": "记录名称",
    "amount": 1000.00,
    "monthly_amount": 100.00,
    "renewal_time": "三个月",
    "renewal_amount": 120.00,
    "remark": "备注信息",
    "is_decreasing": false
}

Response:
{
    "success": true,
    "message": "记录创建成功",
    "data": {
        // 完整的记录对象
    }
}
```

#### 6.3.3 更新记录状态
```
POST /api/records/{book_id}/{record_id}/toggle
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
    "month": "2024-01", // 可选，默认当前月份
    "completed": true   // 目标状态
}

Response:
{
    "success": true,
    "message": "状态更新成功",
    "data": {
        "record_id": 1,
        "month": "2024-01",
        "completed": true,
        "accumulated_amount": 400.00,
        "remaining_amount": 600.00
    }
}
```

### 6.4 统计分析接口

#### 6.4.1 获取总览统计
```
GET /api/statistics/overview
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": {
        "total_books": 3,
        "total_records": 25,
        "completed_records": 15,
        "total_accumulated": 5000.00,
        "monthly_income": 1200.00,
        "month_records": 8,
        "month_completed": 5,
        "month_income": 800.00,
        "month_accumulated": 600.00
    }
}
```

#### 6.4.2 获取月度统计
```
GET /api/statistics/monthly?year={YYYY}
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": [
        {
            "month": "2024-01",
            "total_amount": 10000.00,
            "completed_amount": 6000.00,
            "completion_rate": 0.6
        }
    ]
}
```

### 6.5 数据导出接口

#### 6.5.1 导出CSV
```
GET /api/export/csv?token={jwt_token}
Authorization: Bearer {token}

Response:
Content-Type: text/csv
Content-Disposition: attachment; filename="accounting_data_2024-01-01.csv"

账本名称,日期,名称,金额,每月金额,续期时间,续期金额,备注,累计金额,是否完成,完成月份,是否锁定,创建时间,更新时间
默认账本,2024-01-01,测试记录,1000.00,100.00,三个月,120.00,测试备注,300.00,0,,0,2024-01-01 00:00:00,2024-01-01 00:00:00
```

#### 6.5.2 导出JSON
```
GET /api/export/json?token={jwt_token}
Authorization: Bearer {token}

Response:
Content-Type: application/json
Content-Disposition: attachment; filename="accounting_data_2024-01-01.json"

{
    "export_info": {
        "timestamp": "2024-01-01 00:00:00",
        "user": {
            "username": "testuser",
            "email": "<EMAIL>"
        }
    },
    "books": [
        {
            "id": 1,
            "name": "默认账本",
            "description": "系统自动创建的默认账本",
            "records": [
                // 记录数组
            ]
        }
    ]
}
```

### 6.6 回收站接口

#### 6.6.1 获取回收站记录
```
GET /api/recycle-bin/records
Authorization: Bearer {token}

Response:
{
    "success": true,
    "data": [
        {
            // 记录对象，包含 original_book_id 和 deleted_at
            "original_book_id": 1,
            "deleted_at": "2024-01-01 00:00:00",
            "original_book_name": "原账本名称"
        }
    ]
}
```

#### 6.6.2 恢复记录
```
POST /api/recycle-bin/restore/{record_id}
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
    "target_book_id": 1 // 可选，指定目标账本
}

Response:
{
    "success": true,
    "message": "记录恢复成功",
    "data": {
        "record_id": 1,
        "target_book": "目标账本名称"
    }
}
```

---

## 7. 业务规则说明

### 7.1 用户管理规则

#### 7.1.1 注册规则
- 用户名：3-50字符，只能包含字母、数字、下划线
- 邮箱：必须是有效的邮箱格式，系统内唯一
- 密码：最少6位字符，建议包含字母和数字
- 注册成功后自动创建默认账本

#### 7.1.2 认证规则
- JWT token有效期7天
- token过期后需要重新登录
- 支持用户名或邮箱登录
- 密码错误次数限制（可扩展）

### 7.2 账本管理规则

#### 7.2.1 账本创建规则
- 账本名称在用户范围内必须唯一
- 账本名称不能为空
- 描述字段可选
- 每个用户至少保留一个账本

#### 7.2.2 账本删除规则
- 不能删除用户的最后一个账本
- 删除账本时，所有记录自动移至回收站
- 回收站账本自动创建（如果不存在）
- 回收站账本不能被删除

### 7.3 记录管理规则

#### 7.3.1 记录创建规则
- 必填字段：date, name, amount, renewal_time
- monthly_amount和renewal_amount默认为0
- renewal_time只能是：一个月、二个月、三个月、六个月、永久
- 金额字段必须为非负数，最多2位小数

#### 7.3.2 记录编辑规则
- 只有记录所有者可以编辑
- 编辑后需要重新计算相关金额
- 锁定的记录不能编辑状态
- 已结束的递减记录提示用户处理

#### 7.3.3 状态管理规则
- 每个记录在每个月份有独立的完成状态
- 当前月份的状态变更会影响累计金额
- 历史月份的状态变更不影响累计金额
- 递减记录完成时会减少剩余金额

### 7.4 计算规则

#### 7.4.1 续期计算规则
- 续期月份 = 记录日期 + N个月（N为续期周期）
- 续期月份使用renewal_amount，其他月份使用monthly_amount
- 永久续期的记录不参与续期计算

#### 7.4.2 累计金额规则
- 累计金额 = 所有已完成月份的金额总和
- 只有当前月份及之前的完成状态才计入累计金额
- 未来月份的状态不影响当前累计金额

#### 7.4.3 递减形式规则
- 剩余金额 = 原始金额 - 累计金额
- 剩余金额不能为负数
- 剩余金额为0时自动标记为已结束
- 已结束的记录不能再完成新的月份

### 7.5 权限控制规则

#### 7.5.1 数据访问权限
- 用户只能访问自己的账本和记录
- 账本所有者才能管理账本下的记录
- 回收站记录只有原所有者可以操作

#### 7.5.2 操作权限
- 创建：用户可以在自己的账本中创建记录
- 编辑：只能编辑自己的记录
- 删除：删除记录会移至回收站
- 恢复：只能恢复自己删除的记录

### 7.6 数据完整性规则

#### 7.6.1 外键约束
- 记录必须关联有效的账本
- 账本必须关联有效的用户
- 月份状态必须关联有效的记录

#### 7.6.2 数据一致性
- 累计金额必须等于所有已完成月份的金额总和
- 递减记录的剩余金额必须等于原始金额减去累计金额
- 已结束的递减记录剩余金额必须为0

---

## 8. 配置参数说明

### 8.1 数据库配置

#### 8.1.1 连接配置
```typescript
// Node.js 数据库连接配置
interface DatabaseConfig {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    ssl?: boolean;
    pool?: {
        min: number;
        max: number;
        idle: number;
    };
}

const dbConfig: DatabaseConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'shuju',
    username: process.env.DB_USER || 'shuju',
    password: process.env.DB_PASS || 'your_secure_password',
    ssl: process.env.NODE_ENV === 'production',
    pool: {
        min: 2,
        max: 10,
        idle: 10000  // 10秒空闲超时
    }
};

// Prisma 连接字符串示例
const DATABASE_URL = `postgresql://${dbConfig.username}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}?schema=public`;
```

#### 8.1.2 环境变量配置
```bash
# .env 文件配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=shuju
DB_USER=shuju
DB_PASS=your_secure_password_here
DATABASE_URL=postgresql://shuju:your_secure_password_here@localhost:5432/shuju?schema=public

# SSL 配置（生产环境）
DB_SSL=true
DB_SSL_REJECT_UNAUTHORIZED=true

# 连接池配置
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE=10000
```

### 8.2 JWT配置

#### 8.2.1 JWT参数
```typescript
// JWT配置接口
interface JWTConfig {
    secret: string;
    algorithm: string;
    expiresIn: string;
    issuer: string;
    audience: string;
}

// JWT配置
const jwtConfig: JWTConfig = {
    secret: process.env.JWT_SECRET || 'your-secret-key-change-this-in-production',
    algorithm: 'HS256',
    expiresIn: '7d',  // 7天过期
    issuer: 'accounting-system',
    audience: 'accounting-users'
};

// JWT payload结构
interface JWTPayload {
    user_id: number;
    username: string;
    email: string;
    iat: number;
    exp: number;
    iss: string;
    aud: string;
}

// JWT 生成示例
import jwt from 'jsonwebtoken';

function generateToken(user: { id: number; username: string; email: string }): string {
    const payload: Omit<JWTPayload, 'iat' | 'exp' | 'iss' | 'aud'> = {
        user_id: user.id,
        username: user.username,
        email: user.email
    };

    return jwt.sign(payload, jwtConfig.secret, {
        algorithm: jwtConfig.algorithm as jwt.Algorithm,
        expiresIn: jwtConfig.expiresIn,
        issuer: jwtConfig.issuer,
        audience: jwtConfig.audience
    });
}
```

#### 8.2.2 安全建议
- JWT_SECRET应该是至少32个字符的随机字符串
- 生产环境中必须更改默认密钥
- 定期轮换JWT密钥以提高安全性
- 使用环境变量存储敏感配置
- 考虑使用 RS256 算法替代 HS256 以提高安全性

### 8.3 性能配置

#### 8.3.1 Node.js性能参数
```typescript
// Node.js 性能优化设置
const performanceConfig = {
    // 内存限制
    maxOldSpaceSize: 2048,  // 2GB

    // 集群配置
    clusterMode: process.env.NODE_ENV === 'production',
    workers: process.env.WORKERS || require('os').cpus().length,

    // 请求限制
    requestTimeout: 30000,  // 30秒
    bodyLimit: '10mb',

    // 压缩配置
    compression: {
        level: 6,
        threshold: 1024  // 1KB
    }
};

// Express 应用配置
import express from 'express';
import compression from 'compression';
import helmet from 'helmet';

const app = express();

// 启用压缩
app.use(compression(performanceConfig.compression));

// 安全头
app.use(helmet());

// 请求大小限制
app.use(express.json({ limit: performanceConfig.bodyLimit }));
app.use(express.urlencoded({ extended: true, limit: performanceConfig.bodyLimit }));
```

#### 8.3.2 缓存配置
```typescript
// 前端缓存配置 (React + TypeScript)
interface CacheConfig {
    l1MaxSize: number;
    l1TTL: number;
    l2TTL: number;
}

const CACHE_CONFIG: Record<string, CacheConfig> = {
    // 用户数据缓存
    user: {
        l1MaxSize: 10,
        l1TTL: 5 * 60 * 1000,      // 5分钟
        l2TTL: 60 * 60 * 1000      // 1小时
    },

    // 账本数据缓存
    books: {
        l1MaxSize: 20,
        l1TTL: 3 * 60 * 1000,      // 3分钟
        l2TTL: 30 * 60 * 1000      // 30分钟
    },

    // 记录数据缓存
    records: {
        l1MaxSize: 30,
        l1TTL: 2 * 60 * 1000,      // 2分钟
        l2TTL: 15 * 60 * 1000      // 15分钟
    }
} as const;

// React Query 缓存配置
export const queryClientConfig = {
    defaultOptions: {
        queries: {
            staleTime: 5 * 60 * 1000,     // 5分钟
            cacheTime: 10 * 60 * 1000,    // 10分钟
            retry: 3,
            refetchOnWindowFocus: false,
        },
        mutations: {
            retry: 1,
        },
    },
};
```

#### 8.3.3 后端查询缓存
```typescript
// 查询缓存配置
interface CacheOptions {
    ttl: number;  // 生存时间（秒）
    maxSize: number;  // 最大缓存条目数
    checkPeriod: number;  // 检查过期间隔（秒）
}

class QueryCache {
    private static readonly config: CacheOptions = {
        ttl: 300,  // 5分钟TTL
        maxSize: 1000,  // 最大1000个缓存条目
        checkPeriod: 60  // 每分钟检查一次过期
    };

    private static cache = new Map<string, { data: any; expires: number }>();

    // 缓存键生成规则
    private static generateKey(query: string, params: any[]): string {
        const crypto = require('crypto');
        const content = query + JSON.stringify(params);
        return crypto.createHash('md5').update(content).digest('hex');
    }

    // 获取缓存
    static get(query: string, params: any[]): any | null {
        const key = this.generateKey(query, params);
        const cached = this.cache.get(key);

        if (cached && cached.expires > Date.now()) {
            return cached.data;
        }

        if (cached) {
            this.cache.delete(key);
        }

        return null;
    }

    // 设置缓存
    static set(query: string, params: any[], data: any): void {
        const key = this.generateKey(query, params);
        const expires = Date.now() + (this.config.ttl * 1000);

        // 检查缓存大小限制
        if (this.cache.size >= this.config.maxSize) {
            this.clearExpired();

            // 如果仍然超过限制，删除最旧的条目
            if (this.cache.size >= this.config.maxSize) {
                const firstKey = this.cache.keys().next().value;
                this.cache.delete(firstKey);
            }
        }

        this.cache.set(key, { data, expires });
    }

    // 清理过期缓存
    private static clearExpired(): void {
        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (value.expires <= now) {
                this.cache.delete(key);
            }
        }
    }
}

// Redis 缓存配置（可选）
interface RedisConfig {
    host: string;
    port: number;
    password?: string;
    db: number;
    ttl: number;
}

const redisConfig: RedisConfig = {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
    ttl: 300  // 5分钟
};
```

### 8.4 日志配置

#### 8.4.1 日志级别
```typescript
// 日志级别枚举
enum LogLevel {
    DEBUG = 'debug',
    INFO = 'info',
    WARN = 'warn',
    ERROR = 'error',
    FATAL = 'fatal'
}

// Winston 日志配置
import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';

const logConfig = {
    level: process.env.LOG_LEVEL || LogLevel.INFO,
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
    ),
    defaultMeta: { service: 'accounting-system' },
    transports: [
        // 控制台输出
        new winston.transports.Console({
            format: winston.format.combine(
                winston.format.colorize(),
                winston.format.simple()
            )
        }),

        // 应用日志文件
        new DailyRotateFile({
            filename: 'logs/application-%DATE%.log',
            datePattern: 'YYYY-MM-DD',
            maxSize: '10m',
            maxFiles: '30d',
            level: LogLevel.INFO
        }),

        // 错误日志文件
        new DailyRotateFile({
            filename: 'logs/error-%DATE%.log',
            datePattern: 'YYYY-MM-DD',
            maxSize: '10m',
            maxFiles: '30d',
            level: LogLevel.ERROR
        })
    ]
};

// 创建日志实例
const logger = winston.createLogger(logConfig);
```

#### 8.4.2 日志文件管理
```typescript
// 日志管理配置
interface LogManagementConfig {
    logDir: string;
    maxFileSize: string;
    retentionDays: string;
    errorLogSeparate: boolean;
    compressionEnabled: boolean;
}

const logManagementConfig: LogManagementConfig = {
    logDir: process.env.LOG_DIR || './logs',
    maxFileSize: process.env.LOG_MAX_SIZE || '10m',
    retentionDays: process.env.LOG_RETENTION_DAYS || '30d',
    errorLogSeparate: true,
    compressionEnabled: true
};

// 日志中间件
import { Request, Response, NextFunction } from 'express';

export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
    const start = Date.now();

    res.on('finish', () => {
        const duration = Date.now() - start;
        logger.info('HTTP Request', {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            duration: `${duration}ms`,
            userAgent: req.get('User-Agent'),
            ip: req.ip
        });
    });

    next();
};

// 错误日志中间件
export const errorLogger = (error: Error, req: Request, res: Response, next: NextFunction) => {
    logger.error('Application Error', {
        error: error.message,
        stack: error.stack,
        method: req.method,
        url: req.url,
        body: req.body,
        params: req.params,
        query: req.query,
        ip: req.ip
    });

    next(error);
};
```

### 8.5 安全配置

#### 8.5.1 CORS配置
```typescript
// CORS 配置
import cors from 'cors';

interface CorsConfig {
    origin: string | string[] | boolean;
    methods: string[];
    allowedHeaders: string[];
    credentials: boolean;
    maxAge: number;
}

const corsConfig: CorsConfig = {
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
    maxAge: 86400  // 24小时
};

// 应用 CORS 中间件
app.use(cors(corsConfig));
```

#### 8.5.2 安全头配置
```typescript
// 安全头配置
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';

// Helmet 安全头配置
const helmetConfig = {
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            imgSrc: ["'self'", "data:", "https:"],
            scriptSrc: ["'self'"],
            connectSrc: ["'self'"]
        }
    },
    crossOriginEmbedderPolicy: false,
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
    }
};

app.use(helmet(helmetConfig));

// 速率限制配置
const rateLimitConfig = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 限制每个IP 15分钟内最多100个请求
    message: {
        error: 'Too many requests from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false
});

app.use('/api/', rateLimitConfig);

// API 特定的速率限制
const apiRateLimitConfig = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 50, // API 更严格的限制
    message: {
        error: 'Too many API requests, please try again later.'
    }
});

app.use('/api/auth/', apiRateLimitConfig);
```

#### 8.5.3 输入验证和清理
```typescript
// 输入验证配置
import { z } from 'zod';
import validator from 'validator';

// 通用验证模式
const commonSchemas = {
    email: z.string().email().max(100),
    password: z.string().min(6).max(255),
    username: z.string().min(3).max(50).regex(/^[a-zA-Z0-9_]+$/),
    amount: z.number().positive().max(999999.99),
    date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
    month: z.string().regex(/^\d{4}-\d{2}$/)
};

// 输入清理中间件
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
    // 清理字符串输入
    const sanitizeObject = (obj: any): any => {
        if (typeof obj === 'string') {
            return validator.escape(obj.trim());
        }
        if (Array.isArray(obj)) {
            return obj.map(sanitizeObject);
        }
        if (obj && typeof obj === 'object') {
            const sanitized: any = {};
            for (const [key, value] of Object.entries(obj)) {
                sanitized[key] = sanitizeObject(value);
            }
            return sanitized;
        }
        return obj;
    };

    req.body = sanitizeObject(req.body);
    req.query = sanitizeObject(req.query);
    req.params = sanitizeObject(req.params);

    next();
};
```

### 8.6 监控配置

#### 8.6.1 性能监控
```typescript
// 性能监控配置
interface MonitoringConfig {
    slowQueryThreshold: number;    // 慢查询阈值（毫秒）
    memoryUsageThreshold: number;  // 内存使用阈值（百分比）
    responseTimeThreshold: number; // 响应时间阈值（毫秒）
    enableProfiling: boolean;      // 是否启用性能分析
    metricsInterval: number;       // 指标收集间隔（毫秒）
}

const monitoringConfig: MonitoringConfig = {
    slowQueryThreshold: 1000,      // 1秒
    memoryUsageThreshold: 0.8,     // 80%
    responseTimeThreshold: 2000,   // 2秒
    enableProfiling: process.env.NODE_ENV === 'development',
    metricsInterval: 30000         // 30秒
};

// 性能监控中间件
import { performance } from 'perf_hooks';

export const performanceMonitor = (req: Request, res: Response, next: NextFunction) => {
    const startTime = performance.now();
    const startMemory = process.memoryUsage();

    res.on('finish', () => {
        const endTime = performance.now();
        const endMemory = process.memoryUsage();
        const duration = endTime - startTime;

        // 记录性能指标
        const metrics = {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            duration: Math.round(duration),
            memoryUsed: endMemory.heapUsed - startMemory.heapUsed,
            timestamp: new Date().toISOString()
        };

        // 慢请求警告
        if (duration > monitoringConfig.responseTimeThreshold) {
            logger.warn('Slow Request Detected', metrics);
        }

        // 记录性能日志
        logger.info('Performance Metrics', metrics);
    });

    next();
};

// 系统资源监控
export class SystemMonitor {
    private static interval: NodeJS.Timeout | null = null;

    static start() {
        this.interval = setInterval(() => {
            const memUsage = process.memoryUsage();
            const cpuUsage = process.cpuUsage();

            const metrics = {
                memory: {
                    rss: memUsage.rss,
                    heapTotal: memUsage.heapTotal,
                    heapUsed: memUsage.heapUsed,
                    external: memUsage.external,
                    usagePercent: memUsage.heapUsed / memUsage.heapTotal
                },
                cpu: {
                    user: cpuUsage.user,
                    system: cpuUsage.system
                },
                uptime: process.uptime(),
                timestamp: new Date().toISOString()
            };

            // 内存使用率警告
            if (metrics.memory.usagePercent > monitoringConfig.memoryUsageThreshold) {
                logger.warn('High Memory Usage Detected', metrics);
            }

            logger.debug('System Metrics', metrics);
        }, monitoringConfig.metricsInterval);
    }

    static stop() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
    }
}
```

#### 8.6.2 错误监控
```typescript
// 错误监控配置
interface ErrorMonitoringConfig {
    logErrors: boolean;
    displayErrors: boolean;
    captureUncaughtExceptions: boolean;
    captureUnhandledRejections: boolean;
    errorReporting: {
        enabled: boolean;
        service?: string;  // 如 Sentry DSN
    };
}

const errorConfig: ErrorMonitoringConfig = {
    logErrors: true,
    displayErrors: process.env.NODE_ENV === 'development',
    captureUncaughtExceptions: true,
    captureUnhandledRejections: true,
    errorReporting: {
        enabled: process.env.NODE_ENV === 'production',
        service: process.env.SENTRY_DSN
    }
};

// 全局错误处理
process.on('uncaughtException', (error: Error) => {
    logger.fatal('Uncaught Exception', {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
    });

    // 优雅关闭
    process.exit(1);
});

process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    logger.error('Unhandled Rejection', {
        reason: reason?.message || reason,
        stack: reason?.stack,
        promise: promise.toString(),
        timestamp: new Date().toISOString()
    });
});

// Express 错误处理中间件
export const globalErrorHandler = (
    error: Error,
    req: Request,
    res: Response,
    next: NextFunction
) => {
    logger.error('Express Error Handler', {
        error: error.message,
        stack: error.stack,
        method: req.method,
        url: req.url,
        body: req.body,
        params: req.params,
        query: req.query,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
    });

    // 不在生产环境显示错误详情
    const errorResponse = {
        success: false,
        message: errorConfig.displayErrors ? error.message : 'Internal Server Error',
        ...(errorConfig.displayErrors && { stack: error.stack })
    };

    res.status(500).json(errorResponse);
};
```

### 8.7 备份配置

#### 8.7.1 自动备份配置
```typescript
// 备份配置接口
interface BackupConfig {
    backupDir: string;
    database: {
        host: string;
        port: number;
        name: string;
        user: string;
        password: string;
    };
    excludePatterns: string[];
    retentionDays: number;
    compression: boolean;
    schedule: string;  // Cron 表达式
}

const backupConfig: BackupConfig = {
    backupDir: process.env.BACKUP_DIR || './Backup',
    database: {
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        name: process.env.DB_NAME || 'shuju',
        user: process.env.DB_USER || 'shuju',
        password: process.env.DB_PASS || 'your_secure_password'
    },
    excludePatterns: [
        'Backup/*',
        'logs/*',
        '.git/*',
        'node_modules/*',
        'dist/*',
        '*.tmp',
        '*.log',
        '.env*'
    ],
    retentionDays: 7,                // 保留7天备份
    compression: true,               // 启用压缩
    schedule: '0 2 * * *'           // 每天凌晨2点执行
};

// 备份服务类
import { spawn } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import * as cron from 'node-cron';

export class BackupService {
    private static config = backupConfig;

    // 数据库备份
    static async backupDatabase(): Promise<string> {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `database-backup-${timestamp}.sql`;
        const filepath = path.join(this.config.backupDir, filename);

        // 确保备份目录存在
        if (!fs.existsSync(this.config.backupDir)) {
            fs.mkdirSync(this.config.backupDir, { recursive: true });
        }

        return new Promise((resolve, reject) => {
            const pgDumpArgs = [
                '-h', this.config.database.host,
                '-p', this.config.database.port.toString(),
                '-U', this.config.database.user,
                '-d', this.config.database.name,
                '--no-password',
                '--verbose',
                '--clean',
                '--no-acl',
                '--no-owner',
                '-f', filepath
            ];

            const pgDump = spawn('pg_dump', pgDumpArgs, {
                env: {
                    ...process.env,
                    PGPASSWORD: this.config.database.password
                }
            });

            pgDump.on('close', (code) => {
                if (code === 0) {
                    logger.info('Database backup completed', { filepath });

                    // 压缩备份文件
                    if (this.config.compression) {
                        this.compressFile(filepath)
                            .then(compressedPath => resolve(compressedPath))
                            .catch(reject);
                    } else {
                        resolve(filepath);
                    }
                } else {
                    reject(new Error(`pg_dump exited with code ${code}`));
                }
            });

            pgDump.on('error', reject);
        });
    }

    // 压缩文件
    private static async compressFile(filepath: string): Promise<string> {
        const compressedPath = filepath + '.gz';

        return new Promise((resolve, reject) => {
            const gzip = spawn('gzip', [filepath]);

            gzip.on('close', (code) => {
                if (code === 0) {
                    resolve(compressedPath);
                } else {
                    reject(new Error(`gzip exited with code ${code}`));
                }
            });

            gzip.on('error', reject);
        });
    }

    // 清理过期备份
    static async cleanupOldBackups(): Promise<void> {
        const backupDir = this.config.backupDir;
        const retentionMs = this.config.retentionDays * 24 * 60 * 60 * 1000;
        const cutoffTime = Date.now() - retentionMs;

        if (!fs.existsSync(backupDir)) {
            return;
        }

        const files = fs.readdirSync(backupDir);

        for (const file of files) {
            const filepath = path.join(backupDir, file);
            const stats = fs.statSync(filepath);

            if (stats.mtime.getTime() < cutoffTime) {
                fs.unlinkSync(filepath);
                logger.info('Deleted old backup file', { filepath });
            }
        }
    }

    // 启动定时备份
    static startScheduledBackup(): void {
        cron.schedule(this.config.schedule, async () => {
            try {
                logger.info('Starting scheduled backup');
                await this.backupDatabase();
                await this.cleanupOldBackups();
                logger.info('Scheduled backup completed');
            } catch (error) {
                logger.error('Scheduled backup failed', { error: error.message });
            }
        });

        logger.info('Scheduled backup started', { schedule: this.config.schedule });
    }
}
```

---

## 9. 依赖关系图

### 9.1 系统架构依赖图

```mermaid
graph TB
    subgraph "前端层"
        A[React应用] --> B[ShadcnUI组件]
        A --> C[Zustand状态管理]
        A --> D[React Router路由]
        A --> E[Axios HTTP客户端]
        A --> F[Tailwind CSS样式]
        A --> G[Font Awesome图标]
    end

    subgraph "API层"
        H[Node.js API服务] --> I[Express.js框架]
        H --> J[JWT认证中间件]
        H --> K[TypeScript类型系统]
        H --> L[业务逻辑层]
        H --> M[数据验证层]
    end

    subgraph "数据层"
        N[PostgreSQL数据库] --> O[用户表]
        N --> P[账本表]
        N --> Q[记录表]
        N --> R[月份状态表]
        N --> S[Prisma ORM]
    end

    subgraph "缓存层"
        T[Redis缓存] --> U[查询缓存]
        T --> V[会话缓存]
        W[内存缓存] --> X[应用缓存]
        Y[LocalStorage] --> Z[前端缓存]
    end

    subgraph "监控层"
        AA[Winston日志] --> BB[应用日志]
        AA --> CC[错误日志]
        DD[性能监控] --> EE[系统指标]
        DD --> FF[请求追踪]
    end

    subgraph "进程管理"
        GG[PM2集群] --> HH[负载均衡]
        GG --> II[进程监控]
        GG --> JJ[自动重启]
    end

    A --> H
    H --> N
    H --> T
    H --> AA
    H --> GG

    style A fill:#61dafb
    style H fill:#68a063
    style N fill:#336791
    style T fill:#dc382d
    style AA fill:#f7df1e
    style GG fill:#2b037a
```

### 9.2 模块依赖关系图

```mermaid
graph LR
    subgraph "认证模块"
        A1[用户注册] --> A2[JWT生成]
        A3[用户登录] --> A2
        A2 --> A4[Token验证]
    end
    
    subgraph "账本模块"
        B1[账本创建] --> B2[权限验证]
        B3[账本编辑] --> B2
        B4[账本删除] --> B5[记录迁移]
        B5 --> C1[回收站]
    end
    
    subgraph "记录模块"
        C2[记录创建] --> C3[状态初始化]
        C4[记录编辑] --> C5[金额重算]
        C6[状态切换] --> C7[累计金额更新]
        C7 --> C8[递减逻辑处理]
    end
    
    subgraph "统计模块"
        D1[数据查询] --> D2[实时计算]
        D2 --> D3[缓存更新]
        D3 --> D4[结果返回]
    end
    
    subgraph "导出模块"
        E1[数据提取] --> E2[格式转换]
        E2 --> E3[文件生成]
    end
    
    A4 --> B2
    A4 --> C2
    A4 --> D1
    A4 --> E1
    
    B1 --> C2
    C7 --> D2
    
    style A1 fill:#ffebee
    style B1 fill:#e8f5e8
    style C2 fill:#e3f2fd
    style D1 fill:#f3e5f5
    style E1 fill:#fff8e1
```

### 9.3 数据流向图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端应用
    participant A as API服务
    participant D as 数据库
    participant C as 缓存层
    
    U->>F: 登录请求
    F->>A: POST /api/auth/login
    A->>D: 验证用户凭据
    D-->>A: 用户信息
    A-->>F: JWT Token
    F-->>U: 登录成功
    
    U->>F: 查看记录
    F->>C: 检查缓存
    alt 缓存命中
        C-->>F: 返回缓存数据
    else 缓存未命中
        F->>A: GET /api/records/{book_id}
        A->>D: 查询记录数据
        D-->>A: 记录列表
        A->>A: 计算累计金额
        A-->>F: 处理后数据
        F->>C: 更新缓存
    end
    F-->>U: 显示记录列表
    
    U->>F: 切换记录状态
    F->>A: POST /api/records/{id}/toggle
    A->>D: 更新月份状态
    A->>D: 重新计算累计金额
    A->>D: 更新记录信息
    D-->>A: 更新结果
    A-->>F: 返回新状态
    F->>C: 清除相关缓存
    F-->>U: 状态更新成功
```

### 9.4 计算逻辑依赖图

```mermaid
graph TD
    A[记录基础数据] --> B[续期月份判断]
    A --> C[月份状态查询]
    
    B --> D[金额类型选择]
    C --> D
    
    D --> E[每月金额计算]
    D --> F[续期金额计算]
    
    E --> G[累计金额计算]
    F --> G
    
    G --> H{是否递减形式?}
    
    H -->|是| I[剩余金额计算]
    H -->|否| J[显示原始金额]
    
    I --> K{剩余金额 <= 0?}
    K -->|是| L[标记为已结束]
    K -->|否| M[继续递减]
    
    G --> N[统计数据汇总]
    J --> N
    I --> N
    
    style A fill:#e1f5fe
    style G fill:#e8f5e8
    style H fill:#fff3e0
    style K fill:#fff3e0
    style N fill:#f3e5f5
```

---

## 10. 部署和运维

### 10.1 环境要求

#### 10.1.1 服务器环境
- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 8+)
- **Web服务器**: Nginx 1.18+ (作为反向代理)
- **Node.js版本**: Node.js 18+ LTS
- **数据库**: PostgreSQL 14+
- **进程管理**: PM2 或 Docker
- **内存**: 最少2GB，推荐4GB+
- **存储**: 最少10GB可用空间

#### 10.1.2 Node.js 依赖要求
```bash
# 全局依赖
npm install -g pm2
npm install -g typescript
npm install -g ts-node

# 系统依赖
sudo apt-get update
sudo apt-get install -y curl wget gnupg2 software-properties-common

# PostgreSQL 客户端工具
sudo apt-get install -y postgresql-client

# 构建工具（某些 npm 包需要）
sudo apt-get install -y build-essential python3
```

#### 10.1.3 Node.js 安装
```bash
# 使用 NodeSource 仓库安装 Node.js LTS
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version

# 配置 npm 镜像（可选，提高下载速度）
npm config set registry https://registry.npmmirror.com/
```

### 10.2 部署步骤

#### 10.2.1 代码部署
```bash
# 1. 克隆代码仓库
git clone <repository-url> /var/www/accounting-system
cd /var/www/accounting-system

# 2. 安装后端依赖
cd backend
npm install --production

# 3. 安装前端依赖并构建
cd ../frontend
npm install
npm run build

# 4. 设置文件权限
sudo chown -R $USER:$USER /var/www/accounting-system
chmod -R 755 /var/www/accounting-system
mkdir -p /var/www/accounting-system/logs
mkdir -p /var/www/accounting-system/Backup
chmod -R 755 /var/www/accounting-system/logs
chmod -R 755 /var/www/accounting-system/Backup

# 5. 配置环境变量
cd /var/www/accounting-system
cp .env.example .env
# 编辑 .env 文件，设置数据库连接等配置
nano .env
```

#### 10.2.2 数据库初始化
```bash
# 1. 安装 PostgreSQL
sudo apt-get update
sudo apt-get install -y postgresql postgresql-contrib

# 2. 启动 PostgreSQL 服务
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 3. 创建数据库和用户
sudo -u postgres psql << EOF
CREATE DATABASE shuju;
CREATE USER shuju WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE shuju TO shuju;
ALTER USER shuju CREATEDB;
\q
EOF

# 4. 导入数据库结构
cd /var/www/accounting-system
psql -h localhost -U shuju -d shuju -f backend/database/init.sql

# 5. 运行数据库迁移（如果使用 Prisma）
cd backend
npx prisma migrate deploy

# 6. 生成 Prisma 客户端
npx prisma generate
```

#### 10.2.3 应用启动配置
```bash
# 1. 编译 TypeScript（如果需要）
cd /var/www/accounting-system/backend
npm run build

# 2. 配置 PM2 生态系统文件
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'accounting-system-api',
    script: './dist/index.js',  // 或 './src/index.ts' 如果使用 ts-node
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    log_file: '../logs/app.log',
    error_file: '../logs/error.log',
    out_file: '../logs/out.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF

# 3. 启动应用
pm2 start ecosystem.config.js --env production

# 4. 保存 PM2 配置
pm2 save
pm2 startup

# 5. 设置静态文件服务（前端构建文件已在前面步骤中构建）
# 前端文件将通过 Nginx 直接服务，后端 API 通过反向代理
```

#### 10.2.4 SSL 证书配置（可选）
```bash
# 1. 安装 Certbot
sudo apt-get install -y certbot python3-certbot-nginx

# 2. 获取 SSL 证书
sudo certbot --nginx -d your-domain.com

# 3. 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 10.3 Web服务器配置

#### 10.3.1 Nginx配置
```nginx
# 上游服务器配置
upstream accounting_api {
    server 127.0.0.1:3001;
    # 如果使用多个实例，可以添加更多服务器
    # server 127.0.0.1:3002;
    # server 127.0.0.1:3003;
}

server {
    listen 80;
    server_name your-domain.com;
    root /var/www/accounting-system/frontend/dist;
    index index.html;

    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 前端路由支持（SPA）
    location / {
        try_files $uri $uri/ /index.html;

        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }

    # API 反向代理
    location /api/ {
        proxy_pass http://accounting_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";

        # 跨域字体文件
        location ~* \.(woff|woff2|ttf|eot)$ {
            add_header Access-Control-Allow-Origin "*";
        }
    }

    # 安全配置
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /(logs|Backup|node_modules|\.env)/ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 健康检查端点
    location /health {
        proxy_pass http://accounting_api/health;
        access_log off;
    }

    # 限制请求大小
    client_max_body_size 10M;

    # 日志配置
    access_log /var/log/nginx/accounting-system.access.log;
    error_log /var/log/nginx/accounting-system.error.log;
}
```

#### 10.3.2 Apache配置（替代方案）
```apache
# 启用必要的模块
# sudo a2enmod proxy
# sudo a2enmod proxy_http
# sudo a2enmod rewrite
# sudo a2enmod headers

<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/accounting-system/frontend/dist

    # 启用压缩
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>

    # 前端路由支持（SPA）
    <Directory "/var/www/accounting-system/frontend/dist">
        AllowOverride All
        Require all granted

        RewriteEngine On
        RewriteBase /

        # 处理前端路由
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteCond %{REQUEST_URI} !^/api/
        RewriteRule . /index.html [L]

        # 安全头
        Header always set X-Frame-Options "SAMEORIGIN"
        Header always set X-Content-Type-Options "nosniff"
        Header always set X-XSS-Protection "1; mode=block"
        Header always set Referrer-Policy "strict-origin-when-cross-origin"
    </Directory>

    # API 反向代理
    ProxyPreserveHost On
    ProxyRequests Off

    <Location /api/>
        ProxyPass http://127.0.0.1:3001/api/
        ProxyPassReverse http://127.0.0.1:3001/api/

        # 设置代理头
        ProxyPassReverse /
        ProxyPassReverseAdjust On

        # 超时设置
        ProxyTimeout 30
    </Location>

    # 健康检查
    <Location /health>
        ProxyPass http://127.0.0.1:3001/health
        ProxyPassReverse http://127.0.0.1:3001/health
    </Location>

    # 静态文件缓存
    <LocationMatch "\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
        Header set Cache-Control "public, immutable"
    </LocationMatch>

    # 安全配置
    <DirectoryMatch "/(logs|Backup|node_modules|\.env)/">
        Require all denied
    </DirectoryMatch>

    <Files ~ "^\.">
        Require all denied
    </Files>

    # 日志配置
    ErrorLog ${APACHE_LOG_DIR}/accounting-system_error.log
    CustomLog ${APACHE_LOG_DIR}/accounting-system_access.log combined

    # 限制请求大小
    LimitRequestBody ********  # 10MB
</VirtualHost>
```

### 10.4 监控和维护

#### 10.4.1 日志监控
```bash
# 1. 设置日志轮转
cat > /etc/logrotate.d/accounting-system << EOF
/var/www/accounting-system/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
EOF

# 2. 监控错误日志
tail -f /var/www/accounting-system/logs/error.log

# 3. 性能日志分析
grep "Slow" /var/www/accounting-system/logs/performance.log
```

#### 10.4.2 数据库维护
```sql
-- 1. 定期优化表（PostgreSQL 使用 VACUUM 和 ANALYZE）
VACUUM ANALYZE users;
VACUUM ANALYZE account_books;
VACUUM ANALYZE records;
VACUUM ANALYZE record_monthly_states;

-- 2. 重建索引（如果需要）
REINDEX TABLE records;
REINDEX TABLE record_monthly_states;

-- 3. 检查索引使用情况
SELECT
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- 4. 查看表统计信息
SELECT
    schemaname,
    tablename,
    n_tup_ins,
    n_tup_upd,
    n_tup_del,
    n_live_tup,
    n_dead_tup,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
WHERE schemaname = 'public';

-- 5. 监控慢查询（需要在 postgresql.conf 中配置）
-- log_min_duration_statement = 1000  # 记录执行时间超过1秒的查询
-- log_statement = 'all'  # 记录所有语句（仅用于调试）

-- 查看当前活动查询
SELECT
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query,
    state
FROM pg_stat_activity
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
AND state = 'active';
```

#### 10.4.3 PostgreSQL 性能监控
```bash
# 1. 监控数据库连接
psql -h localhost -U shuju -d shuju -c "
SELECT
    count(*) as total_connections,
    count(*) FILTER (WHERE state = 'active') as active_connections,
    count(*) FILTER (WHERE state = 'idle') as idle_connections
FROM pg_stat_activity;"

# 2. 监控数据库大小
psql -h localhost -U shuju -d shuju -c "
SELECT
    pg_database.datname,
    pg_size_pretty(pg_database_size(pg_database.datname)) AS size
FROM pg_database
WHERE datname = 'shuju';"

# 3. 监控表大小
psql -h localhost -U shuju -d shuju -c "
SELECT
    tablename,
    pg_size_pretty(pg_total_relation_size(tablename::regclass)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(tablename::regclass) DESC;"

# 4. 检查锁等待
psql -h localhost -U shuju -d shuju -c "
SELECT
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.DATABASE IS NOT DISTINCT FROM blocked_locks.DATABASE
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.GRANTED;"
```

#### 10.4.4 备份策略
```bash
# 1. 创建备份脚本
cat > /usr/local/bin/accounting-backup.sh << 'EOF'
#!/bin/bash

# 配置变量
BACKUP_DIR="/var/www/accounting-system/Backup"
DB_NAME="shuju"
DB_USER="shuju"
DB_HOST="localhost"
DB_PORT="5432"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/database_backup_$TIMESTAMP.sql"
LOG_FILE="/var/www/accounting-system/logs/backup.log"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 记录开始时间
echo "$(date): Starting database backup" >> "$LOG_FILE"

# 执行备份
PGPASSWORD="$DB_PASS" pg_dump \
    -h "$DB_HOST" \
    -p "$DB_PORT" \
    -U "$DB_USER" \
    -d "$DB_NAME" \
    --no-password \
    --verbose \
    --clean \
    --no-acl \
    --no-owner \
    -f "$BACKUP_FILE" 2>> "$LOG_FILE"

if [ $? -eq 0 ]; then
    echo "$(date): Database backup completed successfully: $BACKUP_FILE" >> "$LOG_FILE"

    # 压缩备份文件
    gzip "$BACKUP_FILE"
    echo "$(date): Backup file compressed: $BACKUP_FILE.gz" >> "$LOG_FILE"

    # 清理7天前的备份文件
    find "$BACKUP_DIR" -name "*.sql.gz" -mtime +7 -delete
    echo "$(date): Old backup files cleaned up" >> "$LOG_FILE"
else
    echo "$(date): Database backup failed" >> "$LOG_FILE"
    exit 1
fi
EOF

# 2. 设置脚本权限
chmod +x /usr/local/bin/accounting-backup.sh

# 3. 设置自动备份 Cron 任务
cat > /etc/cron.d/accounting-backup << EOF
# 每天凌晨2点执行备份
0 2 * * * root /usr/local/bin/accounting-backup.sh
EOF

# 4. 手动测试备份脚本
/usr/local/bin/accounting-backup.sh

# 5. 备份文件清理（额外的清理脚本）
cat > /usr/local/bin/accounting-cleanup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/var/www/accounting-system/Backup"
LOG_DIR="/var/www/accounting-system/logs"

# 清理30天前的备份文件
find "$BACKUP_DIR" -name "*.sql.gz" -mtime +30 -delete
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +30 -delete

# 清理30天前的日志文件
find "$LOG_DIR" -name "*.log" -mtime +30 -delete

echo "$(date): Cleanup completed" >> "$LOG_DIR/cleanup.log"
EOF

chmod +x /usr/local/bin/accounting-cleanup.sh

# 6. 设置每周清理任务
cat >> /etc/cron.d/accounting-backup << EOF
# 每周日凌晨3点执行清理
0 3 * * 0 root /usr/local/bin/accounting-cleanup.sh
EOF
```

### 10.5 性能优化

#### 10.5.1 Node.js 应用优化
```bash
# 1. PM2 集群模式配置
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'accounting-system-api',
    script: './dist/index.js',
    instances: 'max',  // 使用所有 CPU 核心
    exec_mode: 'cluster',
    max_memory_restart: '1G',
    node_args: [
      '--max-old-space-size=1024',  // 1GB 堆内存
      '--optimize-for-size'         // 优化内存使用
    ],
    env: {
      NODE_ENV: 'production',
      UV_THREADPOOL_SIZE: 128       // 增加线程池大小
    }
  }]
};
EOF

# 2. Node.js 运行时优化
export NODE_OPTIONS="--max-old-space-size=1024 --optimize-for-size"

# 3. 启用 HTTP/2（如果使用 HTTPS）
# 在 Nginx 配置中添加：
# listen 443 ssl http2;
```

#### 10.5.2 PostgreSQL 优化
```bash
# 1. 编辑 PostgreSQL 配置文件
sudo nano /etc/postgresql/14/main/postgresql.conf

# 2. 主要优化参数
cat >> /etc/postgresql/14/main/postgresql.conf << 'EOF'

# 内存配置
shared_buffers = 256MB                    # 25% of RAM
effective_cache_size = 1GB                # 75% of RAM
work_mem = 4MB                           # 每个查询操作的内存
maintenance_work_mem = 64MB              # 维护操作内存

# 连接配置
max_connections = 200                     # 最大连接数
superuser_reserved_connections = 3       # 超级用户保留连接

# WAL 配置
wal_buffers = 16MB                       # WAL 缓冲区
checkpoint_completion_target = 0.9       # 检查点完成目标
wal_writer_delay = 200ms                 # WAL 写入延迟

# 查询规划器配置
random_page_cost = 1.1                   # SSD 优化
effective_io_concurrency = 200           # SSD 并发 I/O

# 日志配置
log_min_duration_statement = 1000        # 记录慢查询（1秒）
log_checkpoints = on                     # 记录检查点
log_connections = on                     # 记录连接
log_disconnections = on                  # 记录断开连接
log_lock_waits = on                      # 记录锁等待

# 自动清理配置
autovacuum = on                          # 启用自动清理
autovacuum_max_workers = 3               # 自动清理工作进程数
autovacuum_naptime = 1min                # 自动清理间隔

EOF

# 3. 重启 PostgreSQL 服务
sudo systemctl restart postgresql

# 4. 验证配置
sudo -u postgres psql -c "SHOW shared_buffers;"
sudo -u postgres psql -c "SHOW effective_cache_size;"
```

#### 10.5.3 系统级优化
```bash
# 1. 文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 2. 内核参数优化
cat >> /etc/sysctl.conf << 'EOF'
# 网络优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000

# 内存优化
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5

# 文件系统优化
fs.file-max = 2097152
EOF

# 应用内核参数
sysctl -p

# 3. 磁盘 I/O 优化（SSD）
echo 'noop' > /sys/block/sda/queue/scheduler  # 或 'deadline'
```

#### 10.5.4 缓存优化
```bash
# 1. 安装和配置 Redis
sudo apt-get update
sudo apt-get install -y redis-server

# 2. 配置 Redis
sudo nano /etc/redis/redis.conf

# 主要配置项：
# maxmemory 512mb
# maxmemory-policy allkeys-lru
# save 900 1
# save 300 10
# save 60 10000

# 3. 启动 Redis 服务
sudo systemctl start redis-server
sudo systemctl enable redis-server

# 4. 验证 Redis 安装
redis-cli ping

# 5. Node.js 应用中的 Redis 配置
cat > backend/src/config/redis.ts << 'EOF'
import Redis from 'ioredis';

const redisConfig = {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
    keepAlive: 30000,
    family: 4,
    keyPrefix: 'accounting:',
};

export const redis = new Redis(redisConfig);

// 缓存中间件
export const cacheMiddleware = (ttl: number = 300) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        const key = `cache:${req.originalUrl}`;

        try {
            const cached = await redis.get(key);
            if (cached) {
                return res.json(JSON.parse(cached));
            }

            // 重写 res.json 以缓存响应
            const originalJson = res.json;
            res.json = function(data: any) {
                redis.setex(key, ttl, JSON.stringify(data));
                return originalJson.call(this, data);
            };

            next();
        } catch (error) {
            logger.warn('Cache middleware error', { error: error.message });
            next();
        }
    };
};
EOF

# 6. Nginx 静态文件缓存优化
cat >> /etc/nginx/sites-available/accounting-system << 'EOF'

# 静态文件缓存配置
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary "Accept-Encoding";

    # 启用 gzip 静态压缩
    gzip_static on;

    # 启用 Brotli 压缩（如果安装了 nginx-module-brotli）
    # brotli_static on;
}

# API 响应缓存（谨慎使用）
location ~* ^/api/(statistics|overview) {
    proxy_pass http://accounting_api;

    # 缓存配置
    proxy_cache_valid 200 5m;
    proxy_cache_key "$scheme$request_method$host$request_uri";
    add_header X-Cache-Status $upstream_cache_status;
}

EOF

# 7. 应用级缓存配置
cat > backend/src/utils/cache.ts << 'EOF'
import NodeCache from 'node-cache';

// 内存缓存实例
export const memoryCache = new NodeCache({
    stdTTL: 300,        // 5分钟默认TTL
    checkperiod: 60,    // 每分钟检查过期
    useClones: false,   // 性能优化
    maxKeys: 1000       // 最大键数量
});

// 多级缓存策略
export class MultiLevelCache {
    private l1Cache = memoryCache;  // L1: 内存缓存
    private l2Cache = redis;        // L2: Redis 缓存

    async get(key: string): Promise<any> {
        // 先检查 L1 缓存
        const l1Value = this.l1Cache.get(key);
        if (l1Value !== undefined) {
            return l1Value;
        }

        // 检查 L2 缓存
        try {
            const l2Value = await this.l2Cache.get(key);
            if (l2Value) {
                const parsed = JSON.parse(l2Value);
                // 回填 L1 缓存
                this.l1Cache.set(key, parsed, 60); // L1 缓存1分钟
                return parsed;
            }
        } catch (error) {
            logger.warn('L2 cache error', { error: error.message });
        }

        return null;
    }

    async set(key: string, value: any, ttl: number = 300): Promise<void> {
        // 设置 L1 缓存
        this.l1Cache.set(key, value, Math.min(ttl, 300));

        // 设置 L2 缓存
        try {
            await this.l2Cache.setex(key, ttl, JSON.stringify(value));
        } catch (error) {
            logger.warn('L2 cache set error', { error: error.message });
        }
    }

    async del(key: string): Promise<void> {
        this.l1Cache.del(key);
        try {
            await this.l2Cache.del(key);
        } catch (error) {
            logger.warn('L2 cache delete error', { error: error.message });
        }
    }
}

export const cache = new MultiLevelCache();
EOF
```

### 10.6 安全加固

#### 10.6.1 文件权限
```bash
# 设置安全的文件权限
find /var/www/accounting-system -type f -exec chmod 644 {} \;
find /var/www/accounting-system -type d -exec chmod 755 {} \;

# 特殊权限设置
chmod 600 /var/www/accounting-system/.env*
chmod 700 /var/www/accounting-system/logs
chmod 700 /var/www/accounting-system/Backup

# Node.js 应用文件权限
chmod +x /var/www/accounting-system/backend/dist/index.js
chown -R node:node /var/www/accounting-system/backend
chown -R www-data:www-data /var/www/accounting-system/frontend/dist

# 敏感文件保护
chmod 600 /var/www/accounting-system/backend/ecosystem.config.js
find /var/www/accounting-system -name "*.key" -exec chmod 600 {} \;
find /var/www/accounting-system -name "*.pem" -exec chmod 600 {} \;
```

#### 10.6.2 防火墙配置
```bash
# UFW 防火墙配置
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许必要的端口
sudo ufw allow 22/tcp          # SSH
sudo ufw allow 80/tcp          # HTTP
sudo ufw allow 443/tcp         # HTTPS

# 限制 SSH 访问（可选）
sudo ufw limit 22/tcp

# 允许特定 IP 访问数据库端口（如果需要远程访问）
# sudo ufw allow from YOUR_IP_ADDRESS to any port 5432

# 启用防火墙
sudo ufw --force enable

# 查看状态
sudo ufw status verbose
```

#### 10.6.3 SSL 证书配置
```bash
# 1. 安装 Certbot
sudo apt-get update
sudo apt-get install -y certbot python3-certbot-nginx

# 2. 获取 SSL 证书
sudo certbot --nginx -d your-domain.com

# 3. 验证证书
sudo certbot certificates

# 4. 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet

# 5. 测试自动续期
sudo certbot renew --dry-run
```

#### 10.6.4 系统安全加固
```bash
# 1. 更新系统
sudo apt-get update && sudo apt-get upgrade -y

# 2. 安装 fail2ban 防止暴力破解
sudo apt-get install -y fail2ban

# 3. 配置 fail2ban
sudo cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
EOF

sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# 4. 配置 SSH 安全
sudo cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup
sudo cat >> /etc/ssh/sshd_config << 'EOF'

# 安全配置
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2
Protocol 2
EOF

sudo systemctl restart sshd

# 5. 禁用不必要的服务
sudo systemctl disable apache2 2>/dev/null || true
sudo systemctl stop apache2 2>/dev/null || true

# 6. 设置日志监控
sudo apt-get install -y logwatch
sudo cat > /etc/cron.daily/00logwatch << 'EOF'
#!/bin/bash
/usr/sbin/logwatch --output mail --mailto <EMAIL> --detail high
EOF
sudo chmod +x /etc/cron.daily/00logwatch
```

#### 10.6.5 应用安全配置
```bash
# 1. 创建专用用户运行 Node.js 应用
sudo useradd --system --shell /bin/false --home-dir /var/www/accounting-system --no-create-home node-app

# 2. 设置应用文件所有权
sudo chown -R node-app:node-app /var/www/accounting-system/backend
sudo chown -R node-app:node-app /var/www/accounting-system/logs

# 3. 配置 PM2 以非特权用户运行
sudo -u node-app pm2 start /var/www/accounting-system/backend/ecosystem.config.js
sudo -u node-app pm2 save
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u node-app --hp /var/www/accounting-system

# 4. 数据库安全配置
sudo -u postgres psql << 'EOF'
-- 创建只读用户（用于备份和监控）
CREATE USER readonly_user WITH ENCRYPTED PASSWORD 'readonly_password';
GRANT CONNECT ON DATABASE shuju TO readonly_user;
GRANT USAGE ON SCHEMA public TO readonly_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO readonly_user;

-- 限制普通用户权限
REVOKE ALL ON SCHEMA public FROM PUBLIC;
GRANT USAGE ON SCHEMA public TO shuju;
GRANT ALL ON ALL TABLES IN SCHEMA public TO shuju;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO shuju;
EOF

# 5. 配置 PostgreSQL 安全
sudo nano /etc/postgresql/14/main/pg_hba.conf
# 确保配置如下：
# local   all             postgres                                peer
# local   all             all                                     md5
# host    all             all             127.0.0.1/32            md5
# host    all             all             ::1/128                 md5

sudo systemctl restart postgresql
```

---

## 总结

本文档提供了记账管理系统的完整技术规范，采用现代化技术栈重构，包括：

1. **项目概述和技术架构** - 明确了系统的目标、功能和现代化技术选型
2. **功能模块详解** - 详细描述了各个功能模块的工作流程
3. **计算逻辑规范** - 精确定义了所有数学计算和业务算法（TypeScript 实现）
4. **数据结构定义** - 完整的数据库设计和前端数据模型（TypeScript 接口）
5. **API接口规范** - 标准化的接口文档和调用方式
6. **业务规则说明** - 明确的业务逻辑和处理规则
7. **配置参数说明** - 详细的系统配置和参数设置
8. **依赖关系图** - 清晰的模块依赖和数据流向
9. **部署和运维** - 完整的部署指南和运维方案

### 技术栈现代化更新亮点：

**前端技术栈升级：**
- **React 19** - 最新版本的 React 框架，提供更好的性能和开发体验
- **TypeScript 严格模式** - 强类型系统，提高代码质量和可维护性
- **Tailwind CSS V4** - 现代化的原子化 CSS 框架
- **ShadcnUI** - 基于 Radix UI 的高质量组件库
- **Font Awesome** - 完整的图标解决方案

**后端技术栈现代化：**
- **Node.js** - 高性能的 JavaScript 运行时环境
- **Express.js** - 成熟稳定的 Web 应用框架
- **TypeScript** - 前后端统一的类型安全语言
- **PM2** - 企业级的进程管理和负载均衡
- **Winston** - 专业的日志管理系统

**数据库技术栈升级：**
- **PostgreSQL** - 企业级关系型数据库，提供更强的数据一致性
- **Prisma/TypeORM** - 现代化的 ORM 工具，类型安全的数据库操作
- **Redis** - 高性能缓存系统，提升应用响应速度
- **多级缓存策略** - 内存缓存 + Redis 缓存的组合方案

**开发体验优化：**
- 完整的 TypeScript 类型定义覆盖前后端
- React Hooks 形式的业务逻辑封装
- 现代化的状态管理方案（Zustand）
- 统一的 JavaScript/TypeScript 技术栈
- 优化的缓存策略和性能配置
- 完善的错误处理和监控系统

**架构优势提升：**
- **全栈 TypeScript** - 前后端类型安全，减少接口错误
- **微服务友好** - Node.js 架构便于后续微服务拆分
- **云原生支持** - 容器化部署和云平台集成
- **高并发处理** - Node.js 异步 I/O 模型提供更好的并发性能
- **数据一致性** - PostgreSQL 提供 ACID 事务保证

此文档确保了：
- **技术栈现代化** - 采用最新的全栈技术方案，提供卓越的开发体验
- **类型安全** - 完整的 TypeScript 类型定义，减少运行时错误
- **组件化设计** - 基于 React 的组件化架构，提高代码复用性
- **性能优化** - 现代化的缓存和优化策略，提升系统响应速度
- **可维护性强** - 清晰的代码结构和完善的类型系统
- **扩展性好** - 微服务架构友好，支持业务快速发展
- **运维便利** - 完善的监控、日志和部署方案

建议在项目重构过程中严格按照此规范执行，充分利用现代化全栈技术的优势，确保系统的稳定性、性能、可维护性和可扩展性。
